import os
import hydra
import torch
from tqdm import tqdm
import torch.optim as optim
# from util import InputPadder
# from core.utils.utils import InputPadder  # Not used in this file
# from core.monster import Monster
from core.selective_igev import IGEVStereo as Monster
# from omegaconf import OmegaConf  # Not used in this file
import torch.nn.functional as F
from accelerate import Accelerator
import core.stereo_datasets as datasets
from accelerate.utils import set_seed
# from accelerate.logging import get_logger  # Not used in this file
from accelerate import DataLoaderConfiguration
from accelerate.utils import DistributedDataParallelKwargs

# import matplotlib  # Not used in this file
# import matplotlib.pyplot as plt  # Not used in this file
# from matplotlib import colors, cm  # Not used in this file
# import numpy as np  # Not used in this file
# import wandb  # Not used in this file
from pathlib import Path
from typing import Mapping


# ================= Helper functions for sequence_loss =================

def _downsample_targets_once(disp_gt: torch.Tensor, valid: torch.Tensor, size_hw: tuple[int, int]):
    """Prepare fp32 GT disparity and valid mask at given low-res size once."""
    H4, W4 = size_hw
    disp_gt_ds = F.interpolate(disp_gt, size=(H4, W4), mode='nearest').to(torch.float32)
    valid_ds = (F.interpolate(valid.float(), size=(H4, W4), mode='nearest') > 0.5)
    return disp_gt_ds, valid_ds


def _confidence_loss_block(disp_preds, conf_list, disp_gt_ds, valid_ds, max_disp: int, weight: float):
    """Compute confidence loss over all iterations; returns (loss_scalar, per_term_count)."""
    if not (isinstance(conf_list, (list, tuple)) and len(conf_list) > 0) or weight <= 0.0:
        return disp_preds[-1].new_tensor(0.0), 0
    with torch.cuda.amp.autocast(enabled=False):
        gamma, alpha, eps = 0.1, 1.0, 1e-6
        charb_eps = 1e-6
        err_cap = float(max_disp) / 4.0
        acc = disp_preds[-1].new_tensor(0.0, dtype=torch.float32)
        count = 0
        num_pairs = min(len(conf_list), len(disp_preds))
        for i in range(num_pairs):
            conf_map = conf_list[i]
            disp_pred_4x = F.interpolate(disp_preds[i].detach(), size=conf_map.shape[-2:], mode='bilinear', align_corners=False) / 4.0
            disp_pred_4x = disp_pred_4x.to(torch.float32)
            # 如果 conf_map 与目标下采样分辨率不一致，则重采样 GT/valid
            if conf_map.shape[-2:] != disp_gt_ds.shape[-2:]:
                gt_ds_i = F.interpolate(disp_gt_ds, size=conf_map.shape[-2:], mode='nearest')
                valid_ds_i = (F.interpolate(valid_ds.float(), size=conf_map.shape[-2:], mode='nearest') > 0.5)
            else:
                gt_ds_i, valid_ds_i = disp_gt_ds, valid_ds
            conf_fp32 = torch.nan_to_num(conf_map.to(torch.float32), nan=0.0).clamp(1e-4, 1.0)
            # 对齐尺度：pred/4 与 gt/4 比较
            err = (disp_pred_4x - gt_ds_i / 4.0).abs()
            err = torch.sqrt(err * err + charb_eps)
            if err_cap > 0:
                err = err.clamp_max(err_cap)
            w = conf_fp32 + eps
            core = gamma * (w * err)
            reg = alpha * (1.0 - conf_fp32).pow(2)
            finite_mask = torch.isfinite(core) & torch.isfinite(reg) & valid_ds_i
            if finite_mask.any():
                term = core[finite_mask].mean() + reg[torch.isfinite(reg)].mean()
                acc = acc + term
                count += 1
        if count > 0:
            return acc / count, count
        else:
            return disp_preds[-1].new_tensor(0.0, dtype=torch.float32), 0


def _semantic_loss_block(mask_sem, disp_gt_ds, valid_ds, tau: float = 2.0):
    """Supervise semantic mask with GT local disparity consistency using hard labels.
    For each center pixel and its 7x7 neighbors: label=1 if |d_i - d_j| <= tau else 0.
    """
    if (mask_sem is None):
        return disp_gt_ds.new_tensor(0.0)
    with torch.cuda.amp.autocast(enabled=False):
        B, K2, H4, W4 = mask_sem.shape
        if K2 != 49:
            return disp_gt_ds.new_tensor(0.0)
        pad = 3
        with torch.no_grad():
            gt_unf = F.unfold(disp_gt_ds, kernel_size=7, padding=pad).view(B, 49, H4, W4)
            gt_cen = disp_gt_ds
            delta = (gt_unf - gt_cen).abs()
            y_hard = (delta <= float(tau)).to(torch.float32)
            valid_unf = F.unfold(valid_ds.float(), kernel_size=7, padding=pad).view(B, 49, H4, W4) > 0.5
            vmask = (valid_unf & valid_ds).to(torch.float32)
        p = mask_sem.to(torch.float32).clamp(1e-6, 1.0 - 1e-6)
        bce = -( y_hard*torch.log(p) + (1.0 - y_hard)*torch.log(1.0 - p) )
        sem_loss = (bce * vmask).sum() / (vmask.sum().clamp_min(1.0))
        return sem_loss


def occlusion_supervision_loss(p_occ0: torch.Tensor | None, occ_mask_full: torch.Tensor | None,
                               weight: float = 0.1, prior_rho: float = 0.2, prior_weight: float = 0.005,
                               pos_weight: float | None = None) -> torch.Tensor:
    """Occlusion supervision with class-imbalanced BCE.

    Args:
        p_occ0: predicted occlusion probability in [0,1], shape ~ (B,1,h,w) or (B,h,w).
        occ_mask_full: GT occlusion mask (0/1 or soft), will be resized to match p_occ0 spatial size.
        weight: coefficient for BCE term. If <= 0, the whole loss returns 0.
        prior_rho: desired global mean occlusion rate.
        prior_weight: coefficient for the prior term |mean(p) - prior_rho|.
        pos_weight: optional positive-class weight (>1 increases the penalty for occluded pixels). If None,
            it will be computed automatically from the batch label ratio as (num_neg / num_pos).
    """
    if (p_occ0 is None) or (weight <= 0.0):
        if isinstance(occ_mask_full, torch.Tensor):
            dev = occ_mask_full.device
        else:
            dev = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        return torch.tensor(0.0, device=dev)

    p = p_occ0.clamp(1e-6, 1.0 - 1e-6)
    loss = p.new_tensor(0.0)

    if isinstance(occ_mask_full, torch.Tensor) and weight > 0.0:
        oc_mask = occ_mask_full
        y = F.interpolate(oc_mask.float(), size=p.shape[-2:], mode='nearest').clamp(0.0, 1.0)
        # Auto compute positive weight if not provided: rarer positives -> larger weight
        if pos_weight is None:
            num_pos = y.sum()
            num_total = y.numel()
            num_neg = num_total - num_pos
            # avoid division by zero; if no positives, fall back to 1.0
            w_pos_t = torch.where(num_pos > 0, (num_neg + 1e-6) / (num_pos + 1e-6), p.new_tensor(1.0))
            w_pos = float(w_pos_t.item())
        else:
            w_pos = float(pos_weight)
        w_neg = 1.0
        bce = -(w_pos * y * torch.log(p) + w_neg * (1.0 - y) * torch.log(1.0 - p))
        loss = loss + bce.mean() * float(weight)

    mean_p = p.mean()
    prior = (mean_p - float(prior_rho)).abs()
    loss = loss + float(prior_weight) * prior
    return loss


def sequence_loss(disp_preds, 
                  disp_init_pred, 
                  disp_gt, valid, 
                  loss_gamma=0.9,
                  max_disp=192, 
                  conf_list=None, 
                  conf_weight: float = 0.05, 
                  mask_sem=None, 
                  sem_weight: float = 0.02, 
                  occ_mask: torch.Tensor | None = None, 
                  p_occ0: torch.Tensor | None = None, 
                  p_occ: torch.Tensor | None = None):
    """ Loss function defined over sequence of flow predictions
    仅对初始视差的损失屏蔽遮挡区域(occ_mask<0.5)。后续序列损失保持不变。
    """

    n_predictions = len(disp_preds)
    assert n_predictions >= 1
    disp_loss = 0.0
    mag = torch.sum(disp_gt**2, dim=1).sqrt()
    valid = ((valid >= 0.5) & (mag < max_disp)).unsqueeze(1)
    assert valid.shape == disp_gt.shape, [valid.shape, disp_gt.shape]
    assert not torch.isinf(disp_gt[valid.bool()]).any()
   
    ###################### 初始视差损失  使用置信度来加权，避免产生路径依赖 #################################
    init_valid = valid.bool() & ~torch.isnan(disp_init_pred)
    if isinstance(p_occ0, torch.Tensor):
        w_init = 1.0 - p_occ0.detach().clamp(0.0, 1.0)
        w_init = F.interpolate(w_init, size=disp_init_pred.shape[-2:], mode='nearest').clamp(0.05, 1.0)
        l1_map = (disp_init_pred - disp_gt).abs()
        weighted = w_init * l1_map
        disp_loss += 1.0 * (weighted[init_valid]).mean()
    else:
        disp_loss += 1.0 * F.l1_loss(disp_init_pred[init_valid], disp_gt[init_valid], reduction='mean')

    ####################### 序列视差损失 #################################
    for i in range(n_predictions):
        adjusted_loss_gamma = loss_gamma**(15/(n_predictions - 1)) if (n_predictions - 1) > 0 else 1.0
        i_weight = adjusted_loss_gamma**(n_predictions - i - 1)
        i_loss = (disp_preds[i] - disp_gt).abs()
        # quantile = torch.quantile(i_loss, 0.9)
        assert i_loss.shape == valid.shape, [i_loss.shape, valid.shape, disp_gt.shape, disp_preds[i].shape]
        disp_loss += i_weight * i_loss[valid.bool() & ~torch.isnan(i_loss)].mean()

    ####################### 置信度/语义监督 #################################
    ds_size = None
    if mask_sem is not None:
        ds_size = mask_sem.shape[-2:]
    elif isinstance(conf_list, (list, tuple)) and len(conf_list) > 0:
        ds_size = conf_list[-1].shape[-2:]
    conf_loss_total = disp_preds[-1].new_tensor(0.0)
    sem_loss_total = disp_preds[-1].new_tensor(0.0)
    if ds_size is not None:
        disp_gt_ds, valid_ds = _downsample_targets_once(disp_gt, valid, ds_size)
        # 置信度损失
        conf_loss_total, _ = _confidence_loss_block(disp_preds, conf_list, disp_gt_ds, valid_ds, max_disp, conf_weight)
        if conf_loss_total.numel() > 0 and conf_weight > 0.0:
            disp_loss = disp_loss + conf_weight * conf_loss_total
        # 语义同域监督
        sem_loss_total = _semantic_loss_block(mask_sem, disp_gt_ds, valid_ds, tau=2.0)
        if sem_loss_total.numel() > 0 and sem_weight > 0.0:
            disp_loss = disp_loss + sem_weight * sem_loss_total

    ####################### 遮挡监督 #################################
    occ_loss0 = occlusion_supervision_loss(p_occ0, occ_mask, weight=1.0, prior_rho=0.1, prior_weight=0.05)
    occ_loss1 = occlusion_supervision_loss(p_occ, occ_mask, weight=1.0, prior_rho=0.1, prior_weight=0.05)
    disp_loss = disp_loss + occ_loss0 + occ_loss1


    epe = torch.sum((disp_preds[-1] - disp_gt)**2, dim=1).sqrt()
    epe = epe.view(-1)[valid.view(-1)]

    if valid.bool().sum() == 0:
        epe = torch.Tensor([0.0]).cuda()

    metrics = {
        'train/epe': epe.mean(),
        'train/1px': (epe < 1).float().mean(),
        'train/3px': (epe < 3).float().mean(),
        'train/5px': (epe < 5).float().mean(),
        'train/conf_loss': conf_loss_total,
        'train/sem_loss': sem_loss_total,
        'train/occ_loss0': occ_loss0,
        'train/occ_loss1': occ_loss1,
    }
    return disp_loss, metrics

def fetch_optimizer(args, model):
    """ Create the optimizer and learning rate scheduler """
    mono_lr_scale = float(getattr(args, 'mono_lr_scale', 0.1))
    feat_decoder_lr_scale = float(getattr(args, 'feat_decoder_lr_scale', 0.1))

    # 基于参数id而非Tensor对象比较，避免逐元素比较导致的形状错误
    mono_params = [p for p in model.mono_encoder.parameters() if p.requires_grad]
    feat_params = [p for p in model.feat_decoder.parameters() if p.requires_grad]
    mono_ids = set(map(id, mono_params))
    feat_ids = set(map(id, feat_params))
    rest_params = [p for p in model.parameters() if (p.requires_grad and (id(p) not in mono_ids) and (id(p) not in feat_ids))]

    params_dict = []
    if len(mono_params) > 0:
        params_dict.append({'params': mono_params, 'lr': args.lr * mono_lr_scale})
    if len(feat_params) > 0:
        params_dict.append({'params': feat_params, 'lr': args.lr * feat_decoder_lr_scale})
    if len(rest_params) > 0:
        params_dict.append({'params': rest_params, 'lr': args.lr})

    optimizer = optim.AdamW(params_dict, lr=args.lr, weight_decay=args.wdecay, eps=1e-8)

    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=[group['lr'] for group in params_dict],
        total_steps=args.total_step+100,
        pct_start=0.05,
        cycle_momentum=False,
        anneal_strategy='linear'
    )

    return optimizer, scheduler

@hydra.main(version_base=None, config_path='config', config_name='train_sceneflow')
def main(cfg):
    set_seed(cfg.seed)
    # logger = get_logger(__name__)  # Not used in this file
    Path(cfg.save_path).mkdir(exist_ok=True, parents=True)
    kwargs = DistributedDataParallelKwargs(find_unused_parameters=True)
    accelerator = Accelerator(mixed_precision='bf16', dataloader_config=DataLoaderConfiguration(use_seedable_sampler=True), kwargs_handlers=[kwargs], step_scheduler_with_optimizer=False)

    train_dataset = datasets.fetch_dataloader(cfg)
    if isinstance(train_dataset, torch.utils.data.DataLoader):
        train_loader = train_dataset
    else:
        assert train_dataset is not None, "fetch_dataloader returned None"
        train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=cfg.batch_size//cfg.num_gpu,
            pin_memory=True, shuffle=True, num_workers=int(4), drop_last=True)

    # aug_params = {}  # Not used in this file

    model = Monster(cfg)
    if not cfg.restore_ckpt.endswith("None"):
        assert cfg.restore_ckpt.endswith(".pth")
        print(f"Loading checkpoint from {cfg.restore_ckpt}")
        assert os.path.exists(cfg.restore_ckpt)
        checkpoint = torch.load(cfg.restore_ckpt, map_location='cpu')
        ckpt = dict()
        if 'state_dict' in checkpoint.keys():
            checkpoint = checkpoint['state_dict']
        for key in checkpoint:
            ckpt[key.replace('module.', '')] = checkpoint[key]

        model.load_state_dict(ckpt, strict=True)
        print(f"Loaded checkpoint from {cfg.restore_ckpt} successfully")
        del ckpt, checkpoint
    optimizer, lr_scheduler = fetch_optimizer(cfg, model)
    train_loader, model, optimizer, lr_scheduler = accelerator.prepare(train_loader, model, optimizer, lr_scheduler)
    model.to(accelerator.device)

    total_step = 0
    should_keep_training = True
    while should_keep_training:
        active_train_loader = train_loader

        model.train()
        model.module.freeze_bn()
        for data in tqdm(active_train_loader, dynamic_ncols=True, disable=not accelerator.is_main_process):
            _, left, right, disp_gt, occ_mask, valid = [x for x in data]
            with accelerator.autocast():
                disp_init_pred, disp_preds, conf_list, mask_sem, p_occ0, p_occ = model(left, right, iters=cfg.train_iters)
            loss, metrics = sequence_loss(disp_preds, disp_init_pred, disp_gt, valid, max_disp=cfg.max_disp, conf_list=conf_list, conf_weight=0.05, mask_sem=mask_sem, sem_weight=0.02, occ_mask=occ_mask, p_occ0=p_occ0, p_occ=p_occ)

            accelerator.backward(loss)
            accelerator.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()
            lr_scheduler.step()
            optimizer.zero_grad()

            total_step += 1
            # 规约loss与metrics（逐项reduce），避免传入Mapping导致类型告警
            loss_red = accelerator.reduce(loss.detach(), reduction='mean')
            if not isinstance(loss_red, torch.Tensor):
                loss_red = torch.tensor(loss_red, dtype=torch.float32)
            loss_scalar = float(loss_red.item())
            metrics_log = {}
            if isinstance(metrics, Mapping):
                for k, v in metrics.items():
                    if isinstance(v, torch.Tensor):
                        v_red = accelerator.reduce(v.detach(), reduction='mean')
                        if not isinstance(v_red, torch.Tensor):
                            v_red = torch.tensor(v_red, dtype=torch.float32)
                        metrics_log[k] = float(v_red.item())
                    elif isinstance(v, (int, float)):
                        metrics_log[k] = float(v)
                    else:
                        # 非数值类型跳过
                        continue

            # Print training metrics every 100 steps
            if total_step % 100 == 0 and accelerator.is_main_process:
                print(f"\n=== Step {total_step} Training Metrics ===")
                print(f"Loss: {loss_scalar:.6f}")
                print(f"Learning Rate: {optimizer.param_groups[-1]['lr']:.8f}")
                print(f"EPE: {metrics_log.get('train/epe', 0.0):.4f}")
                print(f"1px: {metrics_log.get('train/1px', 0.0):.4f}")
                print(f"3px: {metrics_log.get('train/3px', 0.0):.4f}")
                print(f"5px: {metrics_log.get('train/5px', 0.0):.4f}")
                print(f"Conf Loss: {metrics_log.get('train/conf_loss', 0.0):.4f}")
                print(f"Sem Loss: {metrics_log.get('train/sem_loss', 0.0):.4f}")
                print(f"Occ Loss0: {metrics_log.get('train/occ_loss0', 0.0):.4f}")
                print(f"Occ Loss1: {metrics_log.get('train/occ_loss1', 0.0):.4f}")
                print("=" * 40)

            if (total_step > 0) and (total_step % cfg.save_frequency == 0):
                if accelerator.is_main_process:
                    save_path = Path(cfg.save_path + '/%d.pth' % (total_step))
                    model_save = accelerator.unwrap_model(model)
                    torch.save(model_save.state_dict(), save_path)
                    del model_save

            if total_step == cfg.total_step:
                should_keep_training = False
                break

    if accelerator.is_main_process:
        save_path = Path(cfg.save_path + '/final.pth')
        model_save = accelerator.unwrap_model(model)
        torch.save(model_save.state_dict(), save_path)
        del model_save
    
    accelerator.end_training()

if __name__ == '__main__':
    main()
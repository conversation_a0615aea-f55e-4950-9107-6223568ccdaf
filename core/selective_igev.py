import torch
import torch.nn as nn
import torch.nn.functional as F
import os
import sys
from typing import Optional
from core.update import BasicSelectiveMultiUpdateBlock, SpatialAttentionExtractor, ChannelAttentionEnhancement
from core.extractor import MultiBasicEncoder, Feature
from core.geometry import Combined_Geo_Encoding_Volume, Combined_InitCorr_Encoding_Volume
from core.submodule import *
import importlib
from core.attn.block import Block
from torch.utils.checkpoint import checkpoint as ckpt



class hourglass(nn.Module):
    def __init__(self, in_channels):
        super(hourglass, self).__init__()

        self.conv1 = nn.Sequential(BasicConv(in_channels, in_channels*2, is_3d=True, bn=True, relu=True, kernel_size=3,
                                             padding=1, stride=2, dilation=1),
                                   BasicConv(in_channels*2, in_channels*2, is_3d=True, bn=True, relu=True, kernel_size=3,
                                             padding=1, stride=1, dilation=1))
                                    
        self.conv2 = nn.Sequential(BasicConv(in_channels*2, in_channels*4, is_3d=True, bn=True, relu=True, kernel_size=3,
                                             padding=1, stride=2, dilation=1),
                                   BasicConv(in_channels*4, in_channels*4, is_3d=True, bn=True, relu=True, kernel_size=3,
                                             padding=1, stride=1, dilation=1))                             

        self.conv3 = nn.Sequential(BasicConv(in_channels*4, in_channels*6, is_3d=True, bn=True, relu=True, kernel_size=3,
                                             padding=1, stride=2, dilation=1),
                                   BasicConv(in_channels*6, in_channels*6, is_3d=True, bn=True, relu=True, kernel_size=3,
                                             padding=1, stride=1, dilation=1)) 


        self.conv3_up = BasicConv(in_channels*6, in_channels*4, deconv=True, is_3d=True, bn=True,
                                  relu=True, kernel_size=(4, 4, 4), padding=(1, 1, 1), stride=(2, 2, 2))

        self.conv2_up = BasicConv(in_channels*4, in_channels*2, deconv=True, is_3d=True, bn=True,
                                  relu=True, kernel_size=(4, 4, 4), padding=(1, 1, 1), stride=(2, 2, 2))

        self.conv1_up = BasicConv(in_channels*2, 8, deconv=True, is_3d=True, bn=False,
                                  relu=False, kernel_size=(4, 4, 4), padding=(1, 1, 1), stride=(2, 2, 2))

        self.agg_0 = nn.Sequential(BasicConv(in_channels*8, in_channels*4, is_3d=True, kernel_size=1, padding=0, stride=1),
                                   BasicConv(in_channels*4, in_channels*4, is_3d=True, kernel_size=3, padding=1, stride=1),
                                   BasicConv(in_channels*4, in_channels*4, is_3d=True, kernel_size=3, padding=1, stride=1),)

        self.agg_1 = nn.Sequential(BasicConv(in_channels*4, in_channels*2, is_3d=True, kernel_size=1, padding=0, stride=1),
                                   BasicConv(in_channels*2, in_channels*2, is_3d=True, kernel_size=3, padding=1, stride=1),
                                   BasicConv(in_channels*2, in_channels*2, is_3d=True, kernel_size=3, padding=1, stride=1))



        self.feature_att_8 = FeatureAtt(in_channels*2, 64)
        self.feature_att_16 = FeatureAtt(in_channels*4, 192)
        self.feature_att_32 = FeatureAtt(in_channels*6, 160)
        self.feature_att_up_16 = FeatureAtt(in_channels*4, 192)
        self.feature_att_up_8 = FeatureAtt(in_channels*2, 64)

    def forward(self, x, features):
        conv1 = self.conv1(x)
        conv1 = self.feature_att_8(conv1, features[1])

        conv2 = self.conv2(conv1)
        conv2 = self.feature_att_16(conv2, features[2])

        conv3 = self.conv3(conv2)
        conv3 = self.feature_att_32(conv3, features[3])

        conv3_up = self.conv3_up(conv3)
        conv2 = torch.cat((conv3_up, conv2), dim=1)
        conv2 = self.agg_0(conv2)
        conv2 = self.feature_att_up_16(conv2, features[2])

        conv2_up = self.conv2_up(conv2)
        conv1 = torch.cat((conv2_up, conv1), dim=1)
        conv1 = self.agg_1(conv1)
        conv1 = self.feature_att_up_8(conv1, features[1])

        conv = self.conv1_up(conv1)

        return conv


class Feat_transfer(nn.Module):
    def __init__(self, dim_list):
        super(Feat_transfer, self).__init__()
        self.conv4x = nn.Sequential(
            nn.Conv2d(in_channels=int(48+dim_list[0]), out_channels=48, kernel_size=5, stride=1, padding=2),
            nn.InstanceNorm2d(48), nn.ReLU()
            )
        self.conv8x = nn.Sequential(
            nn.Conv2d(in_channels=int(64+dim_list[0]), out_channels=64, kernel_size=5, stride=1, padding=2),
            nn.InstanceNorm2d(64), nn.ReLU()
            )
        self.conv16x = nn.Sequential(
            nn.Conv2d(in_channels=int(192+dim_list[0]), out_channels=192, kernel_size=5, stride=1, padding=2),
            nn.InstanceNorm2d(192), nn.ReLU()
            )
        self.conv32x = nn.Sequential(
            nn.Conv2d(in_channels=dim_list[0], out_channels=160, kernel_size=3, stride=1, padding=1),
            nn.InstanceNorm2d(160), nn.ReLU()
            )
        self.conv_up_32x = nn.ConvTranspose2d(160,
                                192,
                                kernel_size=3,
                                padding=1,
                                output_padding=1,
                                stride=2,
                                bias=False)
        self.conv_up_16x = nn.ConvTranspose2d(192,
                                64,
                                kernel_size=3,
                                padding=1,
                                output_padding=1,
                                stride=2,
                                bias=False)
        self.conv_up_8x = nn.ConvTranspose2d(64,
                                48,
                                kernel_size=3,
                                padding=1,
                                output_padding=1,
                                stride=2,
                                bias=False)
        
        self.res_16x = nn.Conv2d(dim_list[0], 192, kernel_size=1, padding=0, stride=1)
        self.res_8x = nn.Conv2d(dim_list[0], 64, kernel_size=1, padding=0, stride=1)
        self.res_4x = nn.Conv2d(dim_list[0], 48, kernel_size=1, padding=0, stride=1)




    def forward(self, features):
        features_mono_list = []
        feat_32x = self.conv32x(features[3])
        feat_32x_up = self.conv_up_32x(feat_32x)
        feat_16x = self.conv16x(torch.cat((features[2], feat_32x_up), 1)) + self.res_16x(features[2])
        feat_16x_up = self.conv_up_16x(feat_16x)
        feat_8x = self.conv8x(torch.cat((features[1], feat_16x_up), 1)) + self.res_8x(features[1])
        feat_8x_up = self.conv_up_8x(feat_8x)
        feat_4x = self.conv4x(torch.cat((features[0], feat_8x_up), 1)) + self.res_4x(features[0])
        features_mono_list.append(feat_4x)
        features_mono_list.append(feat_8x)
        features_mono_list.append(feat_16x)
        features_mono_list.append(feat_32x)
        return features_mono_list


class Feat_transfer_cnet(nn.Module):
    def __init__(self, dim_list, output_dim):
        super(Feat_transfer_cnet, self).__init__()

        self.res_16x = nn.Conv2d(dim_list[0]+192, output_dim, kernel_size=3, padding=1, stride=1)
        self.res_8x = nn.Conv2d(dim_list[0]+96, output_dim, kernel_size=3, padding=1, stride=1)
        self.res_4x = nn.Conv2d(dim_list[0]+48, output_dim, kernel_size=3, padding=1, stride=1)

    def forward(self, features, stem_x_list):
        features_list = []
        feat_16x = self.res_16x(torch.cat((features[2], stem_x_list[0]), 1))
        feat_8x = self.res_8x(torch.cat((features[1], stem_x_list[1]), 1))
        feat_4x = self.res_4x(torch.cat((features[0], stem_x_list[2]), 1))
        features_list.append([feat_4x, feat_4x])
        features_list.append([feat_8x, feat_8x])
        features_list.append([feat_16x, feat_16x])
        return features_list


# 匹配特征投影：96→128→128→96 残差瓶颈，可选GN，末端L2归一化
class MatchProjection(nn.Module):
    def __init__(self, in_channels: int = 96, hidden: int = 128, out_channels: int = 96, use_gn: bool = True, num_groups: int = 32):
        super().__init__()
        Norm = (lambda c: nn.GroupNorm(num_groups=min(num_groups, c), num_channels=c)) if use_gn else (lambda c: nn.Identity())
        self.proj = nn.Sequential(
            nn.Conv2d(in_channels, hidden, kernel_size=3, stride=1, padding=1, bias=False),
            Norm(hidden),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden, hidden, kernel_size=3, stride=1, padding=1, bias=False),
            Norm(hidden),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden, out_channels, kernel_size=1, stride=1, padding=0, bias=True),
        )
        # 残差短接（通道匹配）
        self.skip = nn.Identity() if in_channels == out_channels else nn.Conv2d(in_channels, out_channels, kernel_size=1, bias=False)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.proj(x) + self.skip(x)


class L2Norm2d(nn.Module):
    def __init__(self, eps: float = 1e-6):
        super().__init__()
        self.eps = eps
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # 按通道做L2归一化，保持每个像素的特征模长为1
        norm = torch.norm(x, p=2, dim=1, keepdim=True).clamp_min(self.eps)
        return x / norm


class LocalNeighborhoodSimilarity(nn.Module):
    def __init__(self, kernel_size: int = 7):
        super().__init__()
        assert kernel_size % 2 == 1, "kernel_size must be odd"
        self.kernel_size = kernel_size
        self.pad = kernel_size // 2

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x: [B, C, H, W]
        b, c, h, w = x.shape
        # 归一化后用点积近似cosine相似度
        x_norm = F.normalize(x, p=2, dim=1)
        # 提取每个位置的k*k邻域特征 [B, C*k*k, H*W]
        patches = F.unfold(x_norm, kernel_size=self.kernel_size, padding=self.pad, stride=1)
        patches = patches.view(b, c, self.kernel_size * self.kernel_size, h * w)
        # 中心像素特征 [B, C, H*W]
        centers = x_norm.view(b, c, h * w)
        # 分块计算相似度，避免一次性展开导致显存峰值
        N = h * w
        num_chunks = 16
        chunk_size = (N + num_chunks - 1) // num_chunks
        sim_flat = x.new_zeros(b, self.kernel_size * self.kernel_size, N)
        for ci in range(num_chunks):
            start = ci * chunk_size
            end = min(N, (ci + 1) * chunk_size)
            if start >= end:
                continue
            centers_c = centers[:, :, start:end]                      # [B, C, n]
            patches_c = patches[:, :, :, start:end]                   # [B, C, k*k, n]
            sim_c = (centers_c.unsqueeze(2) * patches_c).sum(dim=1)  # [B, k*k, n]
            sim_flat[:, :, start:end] = sim_c
        sim = sim_flat.view(b, self.kernel_size * self.kernel_size, h, w).contiguous()
        return sim


# 轻量 3D 堆叠分类头（A）
class Stacked3DHead(nn.Module):
    def __init__(self, in_channels: int, mid_channels: int = 16, out_channels: int = 1, num_groups: int = 8, bias: bool = True):
        super().__init__()
        gn = lambda c: nn.GroupNorm(num_groups=min(num_groups, c), num_channels=c)
        self.net = nn.Sequential(
            nn.Conv3d(in_channels, mid_channels, kernel_size=3, stride=1, padding=1, bias=bias),
            gn(mid_channels),
            nn.ReLU(inplace=True),
            nn.Conv3d(mid_channels, mid_channels, kernel_size=3, stride=1, padding=1, bias=bias),
            gn(mid_channels),
            nn.ReLU(inplace=True),
            nn.Conv3d(mid_channels, out_channels, kernel_size=3, stride=1, padding=1, bias=True),
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.net(x)


# 轻量语义同域预测头：1x1 -> GN -> ReLU -> 3x3 DWConv -> GN -> ReLU -> 1x1
class SemanticHeadLight(nn.Module):
    def __init__(self, in_channels: int = 49, hidden: int = 64, num_groups: int = 32):
        super().__init__()
        self.proj1 = nn.Conv2d(in_channels, hidden, kernel_size=1, bias=False)
        self.gn1 = nn.GroupNorm(num_groups=min(num_groups, hidden), num_channels=hidden)
        self.dw = nn.Conv2d(hidden, hidden, kernel_size=3, padding=1, groups=hidden, bias=False)
        self.gn2 = nn.GroupNorm(num_groups=min(num_groups, hidden), num_channels=hidden)
        self.out = nn.Conv2d(hidden, in_channels, kernel_size=1, bias=True)
        self.act = nn.ReLU(inplace=True)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.proj1(x)
        x = self.gn1(x)
        x = self.act(x)
        x = self.dw(x)
        x = self.gn2(x)
        x = self.act(x)
        x = self.out(x)
        return x


class IGEVStereo(nn.Module):
    def __init__(self, args):
        super().__init__()
        self.args = args
        
        context_dims = args.hidden_dims

        self.intermediate_layer_idx = {
            'vits': [2, 5, 8, 11],
            'vitb': [2, 5, 8, 11], 
            'vitl': [4, 11, 17, 23], 
            'vitg': [9, 19, 29, 39]
        }
        mono_model_configs = {
            'vits': {'encoder': 'vits', 'features': 64, 'out_channels': [48, 96, 192, 384]},
            'vitb': {'encoder': 'vitb', 'features': 128, 'out_channels': [96, 192, 384, 768]},
            'vitl': {'encoder': 'vitl', 'features': 256, 'out_channels': [256, 512, 1024, 1024]},
            'vitg': {'encoder': 'vitg', 'features': 384, 'out_channels': [1536, 1536, 1536, 1536]}
        }
        dim_list_ = mono_model_configs[self.args.encoder]['features']
        dim_list = []
        dim_list.append(dim_list_)

        # self.cnet = MultiBasicEncoder(output_dim=[args.hidden_dims, context_dims], norm_fn="batch", downsample=args.n_downsample)
        self.update_block = BasicSelectiveMultiUpdateBlock(self.args, hidden_dims=args.hidden_dims)
        # 使用局部7x7相似度注意力，输出[B,49,H,W]
        self.lns = LocalNeighborhoodSimilarity(kernel_size=7)
        self.cam = ChannelAttentionEnhancement(128)

        
        self.self_attention = Block(
            dim=128,
            num_heads=4,
            mlp_ratio=4.0,
            qkv_bias=True,
            drop=0.0,
            attn_drop=0.0,
            drop_path=0.0
        )
        # 轻量化语义区域预测头（稳定优先）：1x1->GN->ReLU->DW3x3->GN->ReLU->1x1
        self.semantic_head = SemanticHeadLight(in_channels=49, hidden=64, num_groups=32)
        # 温度参数（训练）：限制在[0.5, 2.0]内用于稳定sigmoid
        self.tau_sem = nn.Parameter(torch.tensor(1.0))
         
        self.stem_2 = nn.Sequential(
            BasicConv_IN(3, 32, kernel_size=3, stride=2, padding=1),
            nn.Conv2d(32, 32, 3, 1, 1, bias=False),
            nn.InstanceNorm2d(32), nn.ReLU()
            )
        self.stem_4 = nn.Sequential(
            BasicConv_IN(32, 48, kernel_size=3, stride=2, padding=1),
            nn.Conv2d(48, 48, 3, 1, 1, bias=False),
            nn.InstanceNorm2d(48), nn.ReLU()
            )
    
        self.stem_8 = nn.Sequential(
            BasicConv_IN(48, 96, kernel_size=3, stride=2, padding=1),
            nn.Conv2d(96, 96, 3, 1, 1, bias=False),
            nn.InstanceNorm2d(96), nn.ReLU()
            )

        self.stem_16 = nn.Sequential(
            BasicConv_IN(96, 192, kernel_size=3, stride=2, padding=1),
            nn.Conv2d(192, 192, 3, 1, 1, bias=False),
            nn.InstanceNorm2d(192), nn.ReLU()
            )

        self.spx = nn.Sequential(nn.ConvTranspose2d(2*32, 9, kernel_size=4, stride=2, padding=1),)
        self.spx_2 = Conv2x_IN(24, 32, True)
        self.spx_4 = nn.Sequential(
            BasicConv_IN(96, 24, kernel_size=3, stride=1, padding=1),
            nn.Conv2d(24, 24, 3, 1, 1, bias=False),
            nn.InstanceNorm2d(24), nn.ReLU()
            )

        self.spx_2_gru = Conv2x(32, 32, True)
        self.spx_gru = nn.Sequential(nn.ConvTranspose2d(2*32, 9, kernel_size=4, stride=2, padding=1),)
 
        # 匹配特征投影：更深的残差瓶颈 + L2Norm（替代原 BasicConv_IN + 1x1）
        self.match_proj = MatchProjection(in_channels=96, hidden=128, out_channels=96, use_gn=True, num_groups=32)
        self.match_l2 = L2Norm2d(eps=1e-6)
 
        # 可选：跨视图注意力融合与softmax温度
        self.use_cross_attention = bool(getattr(self.args, 'use_cross_attention', False))
        self.softmax_temp = float(getattr(self.args, 'softmax_temp', 1.0))
        # 传播的空间距离权重参数（像素为单位的高斯sigma）
        self._spatial_sigma = float(getattr(self.args, 'spatial_sigma', 2.0))

        # 1/8尺度位置编码（ConvPos）与窗口注意力的配置
        self.use_window_attn_1_8 = True
        self.convpos_1_8 = nn.Conv2d(self.args.hidden_dims[0], self.args.hidden_dims[0], kernel_size=3, stride=1, padding=1, groups=self.args.hidden_dims[0])
        self.window_size = 7
 
         
        self.corr_stem = BasicConv(8, 8, is_3d=True, kernel_size=3, stride=1, padding=1)
        self.corr_feature_att = FeatureAtt(8, 96)
        self.cost_agg = hourglass(8)
        self.classifier = nn.Conv3d(8, 1, 3, 1, 1, bias=False)
        # 细化阶段使用独立的分类头；若预训练缺失该权重，将在加载后从classifier复制
        self.classifier_refine = nn.Conv3d(8, 1, 3, 1, 1, bias=False)
        # 遮挡dustbin：沿D池化(avg+max)后的小型2D头，输出[B,1,H,W]作为occlogit
        self.occ_head = nn.Sequential(
            nn.Conv2d(16, 32, kernel_size=3, stride=1, padding=1, bias=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 1, kernel_size=3, stride=1, padding=1, bias=True),
        )



        # 动态导入Depth-Anything-V2，避免静态分析器误报
        try:
            dpt_module = importlib.import_module('depth_anything_v2.dpt')
            DepthAnythingV2 = getattr(dpt_module, 'DepthAnythingV2')
            DepthAnythingV2_decoder = getattr(dpt_module, 'DepthAnythingV2_decoder')
        except Exception as e:
            # 分布式/不同工作目录下，尝试自动加入本地相对路径后再次导入
            candidates = [
                os.path.abspath(os.path.join(os.getcwd(), 'Depth-Anything-V2-list3')),
                os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'Depth-Anything-V2-list3')),
                os.path.abspath(os.path.join(os.path.dirname(__file__), 'Depth-Anything-V2-list3')),
            ]
            for p in candidates:
                if os.path.isdir(p) and p not in sys.path:
                    sys.path.insert(0, p)
            try:
                dpt_module = importlib.import_module('depth_anything_v2.dpt')
                DepthAnythingV2 = getattr(dpt_module, 'DepthAnythingV2')
                DepthAnythingV2_decoder = getattr(dpt_module, 'DepthAnythingV2_decoder')
            except Exception as e2:
                raise ImportError(
                    'Cannot import depth_anything_v2.dpt. Please ensure the submodule is installed and in PYTHONPATH. '
                    'Tried to append local Depth-Anything-V2-list3. Original error: ' + str(e2)
                )
        depth_anything = DepthAnythingV2(**mono_model_configs[args.encoder])
        depth_anything_decoder = DepthAnythingV2_decoder(**mono_model_configs[args.encoder])
        # state_dict_dpt = torch.load(f'./pretrained/depth_anything_v2_{args.encoder}.pth', map_location='cpu')
        # depth_anything.load_state_dict(state_dict_dpt, strict=True)
        # depth_anything_decoder.load_state_dict(state_dict_dpt, strict=False)
        self.mono_encoder = depth_anything.pretrained
        # self.mono_decoder = depth_anything.depth_head
        self.feat_decoder = depth_anything_decoder.depth_head
        # 显式放开mono_encoder进行微调（优化器里会用较小LR）
        self.mono_encoder.requires_grad_(True)
        # self.mono_encoder.requires_grad_(False)
        # self.mono_decoder.requires_grad_(False)

        # 固定传播与稳定性策略
        self._residual_cap_value = 2.0  # 残差硬截断阈值
        self._propagation_topk = 9      # 仅选取TOP-9邻域
        del depth_anything, depth_anything_decoder

        self.feat_transfer = Feat_transfer(dim_list)
        self.feat_transfer_cnet = Feat_transfer_cnet(dim_list, output_dim=args.hidden_dims[0])

        # 初始化末尾：按优先级部分加载预训练权重
        # self._load_partial_pretrained()


    def freeze_bn(self):
        for m in self.modules():
            if isinstance(m, nn.BatchNorm2d):
                m.eval()

    def apply_self_attention_2d(self, x: torch.Tensor) -> torch.Tensor:
        """将[B, C, H, W]特征映射到[B, HW, C]做自注意力，再恢复回[B, C, H, W]。
        适配不同尺度(H, W不同)而无需改动通道数C。
        """
        b, c, h, w = x.shape
        x_seq = x.flatten(2).transpose(1, 2).contiguous()  # [b, hw, c]
        x_seq = self.self_attention(x_seq)  # 期望输入/输出形状均为[b, hw, c]
        x_out = x_seq.transpose(1, 2).reshape(b, c, h, w).contiguous()
        return x_out

    def apply_window_attention_with_convpos(self, x: torch.Tensor, window_size: int = 7) -> torch.Tensor:
        """在2D特征上先加轻量ConvPos，再做窗口级注意力（7x7），最后拼回。
        要求 H,W 能被 window_size 整除；否则做边缘padding。
        """
        b, c, h, w = x.shape
        # ConvPos（depthwise conv）
        x = x + self.convpos_1_8(x)

        pad_h = (window_size - (h % window_size)) % window_size
        pad_w = (window_size - (w % window_size)) % window_size
        if pad_h > 0 or pad_w > 0:
            x = F.pad(x, (0, pad_w, 0, pad_h))
            h2, w2 = h + pad_h, w + pad_w
        else:
            h2, w2 = h, w

        # 切成窗口[B, nWh, nWw, ws, ws, C]
        ws = window_size
        x_ = x.view(b, c, h2 // ws, ws, w2 // ws, ws).permute(0, 2, 4, 3, 5, 1).contiguous()
        x_ = x_.view(-1, ws * ws, c)  # [B*nWh*nWw, ws*ws, C]

        # 使用现有Block（self.self_attention）在窗口内做注意力
        x_ = self.self_attention(x_)

        # 还原回[B, C, H2, W2]
        x_ = x_.view(b, h2 // ws, w2 // ws, ws, ws, c).permute(0, 5, 1, 3, 2, 4).contiguous()
        x_ = x_.reshape(b, c, h2, w2)

        if pad_h > 0 or pad_w > 0:
            x_ = x_[:, :, :h, :w]

        return x_

    # B: 构建D维位置编码（绝对d索引映射到[-1,1]）
    def _build_d_pos_encoding(self, B: int, D: int, H: int, W: int, device, dtype, start_idx: int = 0, total_D: int = -1) -> torch.Tensor:
        if total_D <= 0:
            total_D = D
        # 绝对索引 -> [-1, 1]
        d_idx = torch.arange(start_idx, start_idx + D, device=device, dtype=dtype)
        denom = max(1, total_D - 1)
        d_norm = (2.0 * d_idx / float(denom)) - 1.0
        pos = d_norm.view(1, 1, D, 1, 1).expand(B, 1, D, H, W).contiguous()
        return pos

    def upsample_disp(self, disp, mask_feat_4, stem_2x):

        # with autocast(enabled=self.args.mixed_precision, dtype=getattr(torch, self.args.precision_dtype, torch.float16)):
        xspx = self.spx_2_gru(mask_feat_4, stem_2x)
        spx_pred = self.spx_gru(xspx)
        spx_pred = F.softmax(spx_pred, 1)
        up_disp = context_upsample(disp*4., spx_pred).unsqueeze(1)

        return up_disp

    def propagate_disp_with_conf(self, disp: torch.Tensor, conf: torch.Tensor, f_lns: torch.Tensor, mask_sem: torch.Tensor) -> torch.Tensor:
        """
        分块（4块）进行邻域残差传播，避免大图显存爆炸：
        - 仅从高置信度且语义同域邻域传播到中心；
        - 在有效邻域内取相似度Top-9进行归一化聚合；
        - 残差硬截断到[-2, 2]；
        - 最终以当前像素置信度作为门控进行残差注入。
        """
        B, _, H, W = disp.shape
        K = 7
        pad = K // 2
        center_idx = (K * K) // 2
        topk = self._propagation_topk  # 仍保留配置，但不再使用topk裁剪

        # 展开邻域（一次性，仅此处会有大张量，后续计算按块进行）
        disp_patches = F.unfold(disp, kernel_size=K, padding=pad)  # [B, 49, H*W]
        conf_patches = F.unfold(conf, kernel_size=K, padding=pad)  # [B, 49, H*W]
        # 相似度与语义掩码（先计算，再展平）
        sim = f_lns.view(B, K*K, H, W)

        # 展平成 [B,49,N]
        N = H * W
        sim = sim.view(B, K*K, N)
        mask_sem = mask_sem.view(B, K*K, N)
        disp_flat = disp.view(B, 1, N)
        conf_flat = conf.view(B, 1, N)
        disp_patches = disp_patches.view(B, K*K, N)
        conf_patches = conf_patches.view(B, K*K, N)

        # 预分配输出（就地构建新视差）
        disp_new_flat = torch.empty_like(disp_flat)

        # 分4块处理，降低峰值显存
        num_chunks = 16
        chunk_size = (N + num_chunks - 1) // num_chunks

        temp = max(self.softmax_temp, 1e-6)

        # 预计算空间距离权重：越靠近中心权重越高（高斯衰减）
        with torch.no_grad():
            coords_1d = torch.arange(K, device=disp.device)
            yy, xx = torch.meshgrid(coords_1d, coords_1d, indexing='ij') if hasattr(torch.meshgrid, '__defaults__') else torch.meshgrid(coords_1d, coords_1d)
            dist2 = (yy - pad).float()**2 + (xx - pad).float()**2
            distance_weight = torch.exp(-dist2 / (2.0 * (self._spatial_sigma ** 2))).view(1, K*K, 1)  # [1,49,1]

        for ci in range(num_chunks):
            start = ci * chunk_size
            end = min(N, (ci + 1) * chunk_size)
            if start >= end:
                continue

            # 切片
            sim_c = sim[:, :, start:end]                  # [B,49,nc]
            mask_sem_c = mask_sem[:, :, start:end]        # [B,49,nc]
            disp_patches_c = disp_patches[:, :, start:end]# [B,49,nc]
            conf_patches_c = conf_patches[:, :, start:end]# [B,49,nc]
            conf_center_c = conf_flat[:, :, start:end]    # [B,1,nc]
            disp_center_c = disp_flat[:, :, start:end]    # [B,1,nc]

            # 有效邻域掩码（高置信度且语义同域），并去掉中心
            high_conf_mask_c = (conf_patches_c > conf_center_c).float()
            high_conf_mask_c[:, center_idx:center_idx+1, :] = 0.0
            valid_mask_c = high_conf_mask_c * mask_sem_c

           
            with torch.cuda.amp.autocast(enabled=False):
                sim_scaled_c_fp32 = sim_c.float() / float(temp)
                valid_mask_c_fp32 = valid_mask_c.float()
                # 恢复Top-9：对无效位置加log(0)=-inf后选topk
                masked_sim_c = sim_scaled_c_fp32 + torch.log(valid_mask_c_fp32 + 1e-12)
                values, indices = torch.topk(masked_sim_c, k=topk, dim=1)  # [B,topk,nc]
                sparse_weights_c = torch.zeros_like(sim_scaled_c_fp32)
                src_vals = torch.exp(values)
                sparse_weights_c.scatter_(1, indices, src_vals)
                # 乘以空间距离权重（靠近中心权重更大）
                sparse_weights_c = sparse_weights_c * distance_weight
                denom_c = sparse_weights_c.sum(dim=1, keepdim=True) + 1e-6
                weights_c = sparse_weights_c / denom_c  # [B,49,nc]
            
            # 邻域聚合与残差
            agg_c = (weights_c * disp_patches_c).sum(dim=1, keepdim=True)  # [B,1,nc]
            
            # 无有效邻域时的回退：直接使用中心值，避免向0偏置
            # 有效邻域统计在valid_mask_c上（非零即有效）
            valid_count_c = (valid_mask_c > 0).sum(dim=1, keepdim=True)  # [B,1,nc]
            no_valid_c = valid_count_c.eq(0)
            if no_valid_c.any():
                agg_c = torch.where(no_valid_c, disp_center_c, agg_c)

            residual_c = agg_c - disp_center_c
            # 残差硬截断
            residual_c = torch.clamp(residual_c, min=-self._residual_cap_value, max=self._residual_cap_value)

            # 门控注入：由邻域相对中心的置信度提升决定（仅统计比中心更高置信的邻域，使用算术均值）
            # 先对有效邻域做算术均值（不加相似度/距离权重）
            sum_conf_neighbors = (conf_patches_c * valid_mask_c).sum(dim=1, keepdim=True)  # [B,1,nc]
            cnt_conf_neighbors = valid_mask_c.sum(dim=1, keepdim=True)  # [B,1,nc]
            mean_conf_neighbors = sum_conf_neighbors / (cnt_conf_neighbors + 1e-6)
            # 与中心置信度之差（只取正差）作为改进度
            rel_gain = (mean_conf_neighbors - conf_center_c).clamp_min(0.0)  # [0,1]
            # 将改进度映射到门控区间（移除最小更新量，允许保持不变），并去梯度避免投机
            g_min, g_max = 0.0, 0.8  # 可做为超参；g_min=0允许不更新
            gate_c = torch.clamp(g_max * rel_gain.detach(), g_min, g_max)
            disp_new_flat[:, :, start:end] = disp_center_c + gate_c * residual_c
            # print((gate_c * residual_c).max(), (gate_c * residual_c).min())

        disp_new = disp_new_flat.view(B, 1, H, W)
        return disp_new

    def infer_mono_ckpt(self, image1, image2):
 
        # height_ori, width_ori = image1.shape[2:]
        resize_image1 = F.interpolate(image1, scale_factor=14 / 16, mode='bilinear', align_corners=True)
        resize_image2 = F.interpolate(image2, scale_factor=14 / 16, mode='bilinear', align_corners=True)
 
        patch_h, patch_w = resize_image1.shape[-2] // 14, resize_image1.shape[-1] // 14
        # 合并左右视图，单次前向
        stacked = torch.cat([resize_image1, resize_image2], dim=0)
        # 使用checkpoint节省显存：对encoder与decoder均进行重计算式反向
        def _enc(x: torch.Tensor):
            feats = self.mono_encoder.get_intermediate_layers(
                x, self.intermediate_layer_idx[self.args.encoder], return_class_token=True
            )
            # 转为tuple以兼容checkpoint返回约束
            return tuple(feats)
        features_tuple = ckpt(_enc, stacked)
        # decoder一次性解码，再按batch拆分
        def _dec(*feats: torch.Tensor):
            return tuple(self.feat_decoder(list(feats), patch_h, patch_w))
        f4x, f8x, f16x, f32x = ckpt(_dec, *features_tuple)
        b = image1.shape[0]
        features_left_4x, features_right_4x = f4x[:b], f4x[b:]
        features_left_8x, features_right_8x = f8x[:b], f8x[b:]
        features_left_16x, features_right_16x = f16x[:b], f16x[b:]
        features_left_32x, features_right_32x = f32x[:b], f32x[b:]
 
        return [features_left_4x, features_left_8x, features_left_16x, features_left_32x], [features_right_4x, features_right_8x, features_right_16x, features_right_32x]

    def infer_mono_plain(self, image1, image2):
 
        resize_image1 = F.interpolate(image1, scale_factor=14 / 16, mode='bilinear', align_corners=True)
        resize_image2 = F.interpolate(image2, scale_factor=14 / 16, mode='bilinear', align_corners=True)
 
        patch_h, patch_w = resize_image1.shape[-2] // 14, resize_image1.shape[-1] // 14
        stacked = torch.cat([resize_image1, resize_image2], dim=0)
        features_stacked = self.mono_encoder.get_intermediate_layers(
            stacked, self.intermediate_layer_idx[self.args.encoder], return_class_token=True
        )
        f4x, f8x, f16x, f32x = self.feat_decoder(features_stacked, patch_h, patch_w)
        b = image1.shape[0]
        features_left_4x, features_right_4x = f4x[:b], f4x[b:]
        features_left_8x, features_right_8x = f8x[:b], f8x[b:]
        features_left_16x, features_right_16x = f16x[:b], f16x[b:]
        features_left_32x, features_right_32x = f32x[:b], f32x[b:]
 
        return [features_left_4x, features_left_8x, features_left_16x, features_left_32x], [features_right_4x, features_right_8x, features_right_16x, features_right_32x]

    def infer_mono(self, image1, image2, use_ckpt: bool = False):
        if use_ckpt:
            return self.infer_mono_ckpt(image1, image2)
        else:
            return self.infer_mono_plain(image1, image2)

    def hierarchical_disparity_estimation(self, match_left, match_right, features_left):
         
        # ---------- helpers (local functions, no behavior change) ----------
        def _build_geo_volume(match_l: torch.Tensor, match_r: torch.Tensor, feats_left: list[torch.Tensor]) -> torch.Tensor:
            gwc = build_gwc_volume(match_l, match_r, self.args.max_disp // 4, 8)
            gwc = self.corr_stem(gwc)
            gwc = self.corr_feature_att(gwc, feats_left[0])
            return self.cost_agg(gwc, feats_left)

        def _initial_disp_with_dustbin(geo_vol: torch.Tensor):
            logits_disp0 = self.classifier(geo_vol).squeeze(1)  # [B,D0,H,W]
            feat_avg0 = geo_vol.mean(dim=2)
            feat_max0, _ = geo_vol.max(dim=2)
            feat_cat0 = torch.cat([feat_avg0, feat_max0], dim=1)
            occlogit0 = self.occ_head(feat_cat0)  # [B,1,H,W]
            logits_all0 = torch.cat([logits_disp0, occlogit0], dim=1)
            prob_all0 = F.softmax(logits_all0, dim=1)
            D0 = logits_disp0.shape[1]
            p_disp0 = prob_all0[:, :D0, :, :]
            p_occ0_local = prob_all0[:, D0:D0+1, :, :]
            prob_local = p_disp0 / (1.0 - p_occ0_local + 1e-6)
            init_disp_local = disparity_regression(prob_local, mindisp=0, maxdisp=self.args.max_disp // 4)
            return init_disp_local, p_occ0_local

        def _select_window(init_disp_local: torch.Tensor):
            with torch.no_grad():
                disp_flat = init_disp_local.reshape(-1).to(torch.float32)
                q_low = torch.quantile(disp_flat, 0.05)
                q_high = torch.quantile(disp_flat, 0.95)
                dmin = max(int(q_low.item() - 4), 0)
                dmax = min(int(q_high.item() + 4), self.args.max_disp // 4)
                if dmax <= dmin:
                    dmin = max(dmin - 2, 0)
                    dmax = min(dmin + 4, self.args.max_disp // 4)
            return dmin, dmax

        def _refine_with_dustbin(geo_vol: torch.Tensor, dmin: int, dmax: int):
            geo_vol_win = geo_vol[:, :, dmin:dmax, :, :]
            logits_disp = self.classifier_refine(geo_vol_win).squeeze(1)
            # D pooling for occ head features
            feat_avg = geo_vol_win.mean(dim=2)
            feat_max, _ = geo_vol_win.max(dim=2)
            feat_cat = torch.cat([feat_avg, feat_max], dim=1)
            occlogit = self.occ_head(feat_cat)
            logits_all = torch.cat([logits_disp, occlogit], dim=1)
            prob_all = F.softmax(logits_all, dim=1)
            Dwin = logits_disp.shape[1]
            p_disp = prob_all[:, :Dwin, :, :]
            p_occ_local = prob_all[:, Dwin:Dwin+1, :, :]
            p_eff_local = p_disp / (1.0 - p_occ_local + 1e-6)
            refine_disp_local = disparity_regression(p_eff_local, mindisp=dmin, maxdisp=dmax)
            return refine_disp_local, p_occ_local, p_disp

        def _confidence_map(p_eff_local: torch.Tensor, refine_disp_local: torch.Tensor, dmin: int):
            with torch.no_grad():
                D1 = p_eff_local.shape[1]
                idx = (refine_disp_local - dmin).round().clamp(0, D1 - 1).long()
                conf_map_list = []
                for o in (-2, -1, 0, 1, 2):
                    idx_o = (idx + o).clamp(0, D1 - 1)
                    conf_map_list.append(p_eff_local.gather(1, idx_o))
                return torch.stack(conf_map_list, dim=1).sum(dim=1).clamp(0.0, 1.0)

        # --------------------------- pipeline ---------------------------
        geo_encoding_volume = _build_geo_volume(match_left, match_right, features_left)
        init_disp, p_occ0 = _initial_disp_with_dustbin(geo_encoding_volume)
        disp_min, disp_max = _select_window(init_disp)
        refine_disp, p_occ, p_eff = _refine_with_dustbin(geo_encoding_volume, disp_min, disp_max)
        init_disp_fused = 0.2*init_disp + 0.8*refine_disp
        del geo_encoding_volume
        conf_map = _confidence_map(p_eff, refine_disp, disp_min)
        del p_eff
        del refine_disp
        del init_disp
        return init_disp_fused, conf_map, p_occ0, p_occ

    # =============== 权重部分加载工具 ===============
    def _normalize_ckpt_state(self, ckpt_obj):
        if isinstance(ckpt_obj, dict):
            if 'state_dict' in ckpt_obj:
                state = ckpt_obj['state_dict']
            elif 'model_state_dict' in ckpt_obj:
                state = ckpt_obj['model_state_dict']
            else:
                state = ckpt_obj
        else:
            state = ckpt_obj
        # 去掉module.前缀
        normalized = {}
        for k, v in state.items():
            k2 = k[7:] if k.startswith('module.') else k
            normalized[k2] = v
        return normalized

    def _load_from_path_partial(self, path: str) -> int:
        if not os.path.exists(path):
            return 0
        try:
            ckpt = torch.load(path, map_location='cpu')
        except Exception:
            return 0
        src = self._normalize_ckpt_state(ckpt)
        dst = self.state_dict()
        loaded = 0
        for k, v in src.items():
            if k in dst and isinstance(v, torch.Tensor) and dst[k].shape == v.shape:
                dst[k] = v
                loaded += 1
        self.load_state_dict(dst, strict=False)
        if loaded > 0:
            print(f"[IGEVStereo] Partially loaded {loaded} tensors from '{path}'.")
        return loaded

    def _load_partial_pretrained(self):
        self._load_from_path_partial('checkpoint/selective_igev/middlebury_train.pth')
        self._load_from_path_partial('checkpoint/monster/middlebury.pth')
    
    def forward(self, image1, image2, iters=12, flow_init=None, test_mode=False):
        """ Estimate disparity between pair of frames """

        image1 = (2 * (image1 / 255.0) - 1.0).contiguous()
        image2 = (2 * (image2 / 255.0) - 1.0).contiguous()
        
        features_mono_left,  features_mono_right = self.infer_mono(image1, image2, use_ckpt=(not test_mode))

        features_left = self.feat_transfer(features_mono_left)
        features_right = self.feat_transfer(features_mono_right)

        stem_2x = self.stem_2(image1)
        stem_4x = self.stem_4(stem_2x)
        stem_8x = self.stem_8(stem_4x)
        stem_16x = self.stem_16(stem_8x)
        stem_2y = self.stem_2(image2)
        stem_4y = self.stem_4(stem_2y)

        stem_x_list = [stem_16x, stem_8x, stem_4x]
        features_left[0] = torch.cat((features_left[0], stem_4x), 1)
        features_right[0] = torch.cat((features_right[0], stem_4y), 1)

        match_left = self.match_l2(self.match_proj(features_left[0]))
        match_right = self.match_l2(self.match_proj(features_right[0]))
        
        init_disp, conf_init, p_occ0, p_occ = self.hierarchical_disparity_estimation(match_left, match_right, features_left)

        # if not test_mode:
        xspx = self.spx_4(features_left[0])
        xspx = self.spx_2(xspx, stem_2x)
        spx_pred = self.spx(xspx)
        spx_pred = F.softmax(spx_pred, 1)

        cnet_list = self.feat_transfer_cnet(features_mono_left, stem_x_list)
        net_list = [torch.tanh(x[0]) for x in cnet_list]
        inp_list = [torch.relu(x[1]) for x in cnet_list]

        inp_list[1] = self.apply_window_attention_with_convpos(inp_list[1], window_size=self.window_size)
        att = [self.cam(x) for x in inp_list]

        f_lns = self.lns(inp_list[0].detach())
        mask_sem = torch.sigmoid(self.semantic_head(f_lns) / self.tau_sem)
 
        geo_block = Combined_InitCorr_Encoding_Volume
        geo_fn = geo_block(match_left.float(), match_right.float(), num_levels=self.args.corr_levels, radius=self.args.corr_radius)
        b, c, h, w = match_left.shape
        coords = torch.arange(w, device=match_left.device).float().reshape(1, 1, w, 1).repeat(b, h, 1, 1).contiguous()
        disp = init_disp
        conf = conf_init
        disp_preds = []
        conf_preds = []

        # GRUs iterations to update disparity
        for itr in range(iters):
            disp = disp.detach()
            conf = conf.detach()
            
            geo_feat = geo_fn(disp, coords)
            net_list, mask_feat_4, delta_disp, conf = self.update_block(net_list, inp_list, geo_feat, disp, att, conf=conf)
            disp = disp + delta_disp
            do_stage = (itr >= iters // 3) and ((itr % 2) == 0 or itr == iters - 1)
            
            if do_stage:
                disp = self.propagate_disp_with_conf(disp, conf, f_lns, mask_sem)
            if test_mode and itr < iters-1:
                continue

            # upsample predictions
            disp_up = self.upsample_disp(disp, mask_feat_4, stem_2x)
            disp_preds.append(disp_up)
            conf_preds.append(conf)

        init_disp = context_upsample(init_disp*4., spx_pred.float()).unsqueeze(1)
        if test_mode:
            return disp_up, init_disp, conf, p_occ

        return init_disp, disp_preds, conf_preds, mask_sem, p_occ0, p_occ